# 订单状态重构总结

## 重构目标
将复杂的 `ORDER_STATE.XXXX` 常量写法改为简单直观的数字判断，提高代码可读性和维护性。

## 修改内容

### 1. 移除的常量定义
```javascript
// 原来的写法 (已移除)
const ORDER_STATE = markRaw({
  PENDING_PAYMENT: '0',
  PENDING_DELIVERY_1: '1', 
  CANCELLED: '2',
  PENDING_DELIVERY_3: '3',
  PARTIAL_SHIPPED: '4',
  SHIPPING: '5',
  PARTIAL_CANCELLED: '6',
  REJECTED: '7',
  REVOKED: '8',
  COMPLETED: '9',
  REFUNDED: '10',
  PARTIAL_REFUNDED: '11',
  PARTIAL_REFUNDING: '12',
  DELETED: '-1'
})
```

### 2. 新的数字判断方式

#### 订单状态对照表
| 状态码 | 中文描述 | 原常量名 |
|--------|----------|----------|
| '0' | 待付款 | PENDING_PAYMENT |
| '1' | 待发货 | PENDING_DELIVERY_1 |
| '2' | 已取消 | CANCELLED |
| '3' | 待发货 | PENDING_DELIVERY_3 |
| '4' | 部分发货 | PARTIAL_SHIPPED |
| '5' | 配送中 | SHIPPING |
| '6' | 部分撤销 | PARTIAL_CANCELLED |
| '7' | 拒收 | REJECTED |
| '8' | 已撤销 | REVOKED |
| '9' | 已签收 | COMPLETED |
| '10' | 已退款 | REFUNDED |
| '11' | 部分退款 | PARTIAL_REFUNDED |
| '12' | 部分退款中 | PARTIAL_REFUNDING |
| '-1' | 已删除 | DELETED |

#### 修改示例
```javascript
// 原来的写法
if (status === ORDER_STATE.PENDING_PAYMENT) {
  // 处理待付款逻辑
}

// 新的写法 (更简单明了)
if (status === '0') {  // 待付款
  // 处理待付款逻辑
}
```

### 3. 修改的文件

#### src/views/WoMall/Order/OrderDetail/OrderDetail.vue
- 移除 `ORDER_STATE` 导入
- 将所有 `ORDER_STATE.XXXX` 替换为对应数字
- 添加注释说明每个数字对应的状态

#### src/composables/useOrderAfterSalesActions.js  
- 移除 `ORDER_STATE` 常量定义
- 移除 `markRaw` 导入
- 将所有状态判断改为数字比较
- 添加详细注释说明状态含义

### 4. 重构优势

1. **可读性提升**: 直接看到数字就知道是什么状态，不需要查找常量定义
2. **维护简化**: 不需要维护复杂的常量对象，减少代码复杂度
3. **性能优化**: 减少对象引用，直接字符串比较更高效
4. **一致性**: 与项目中其他文件的写法保持一致

### 5. 注意事项

- 所有状态值都是字符串类型，如 '0', '1', '2' 等
- 在比较时使用严格相等 `===` 
- 添加了详细的注释说明每个数字对应的状态含义
- 保持了原有的业务逻辑不变

## 测试验证

已通过测试验证以下功能正常：
- 物流模块显示逻辑
- 地址模块显示逻辑  
- 价格标签和颜色逻辑
- 底部操作按钮逻辑
- 售后申请逻辑

重构完成后，代码更加简洁明了，后期维护更加方便。
