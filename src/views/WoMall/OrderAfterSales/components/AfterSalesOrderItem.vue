<template>
  <div class="after-sales-order-item">
    <WoCard>
      <header class="after-sales-order-item__header">
        <div class="after-sales-order-item__number-container">
          <span class="after-sales-order-item__number-text">
            服务单号：{{ orderData.afterSaleId || '暂无服务单号' }}
          </span>
          <img
            v-if="orderData.afterSaleId"
            src="@/static/images/copy.png"
            alt="复制"
            class="after-sales-order-item__copy-icon"
            loading="lazy"
            @click.stop="handleCopyOrderNumber(orderData.id)"
          />
        </div>
        <div class="after-sales-order-item__status">
          {{ orderStateText }}
        </div>
      </header>
      <section class="after-sales-order-item__goods">
        <AfterSaleGoodsCard
          :item="orderData"
          :image-size="75"
          :min-height="110"
          :show-actions="true"
          :item-id="orderData.id"
        >
          <template #actions>
            <WoButton
              v-for="action in actionButtons"
              :key="action.key"
              :type="action.type || 'primary'"
              size="small"
              @click.stop="action.handler"
            >
              {{ action.label }}
            </WoButton>
          </template>
        </AfterSaleGoodsCard>
      </section>
    </WoCard>
  </div>
</template>

<script setup>
import { computed, toRefs } from 'vue'
import { debounce } from 'lodash-es'
import useClipboard from 'vue-clipboard3'
import { showToast } from 'vant'
import WoCard from '@components/WoElementCom/WoCard.vue'
import AfterSaleGoodsCard from '@components/GoodsCommon/AfterSaleGoodsCard.vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import orderState from '@/utils/orderState.js'

const props = defineProps({
  orderData: {
    type: Object,
    required: true
  },
  actionButtons: {
    type: Array,
    default: () => []
  }
})

const { orderData, actionButtons } = toRefs(props)
const { toClipboard } = useClipboard()

const orderStateText = computed(() => {
  return orderState(orderData.value?.orderState)
})

const handleCopyOrderNumber = debounce(async (orderNumber) => {
  try {
    await toClipboard(String(orderNumber))
    showToast('复制成功')
  } catch (e) {
    console.error('复制失败:', e)
    showToast('复制失败')
  }
}, 300)
</script>

<style scoped lang="less">
.after-sales-order-item {
  margin-bottom: 10px;

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 11px;
  }

  &__number-container {
    display: flex;
    align-items: center;
    margin-right: 15px;
    width: 100%;
    overflow: hidden;
  }

  &__number-text {
    font-size: @font-size-11;
    color: @text-color-secondary;
    margin-right: 3px;
    .ellipsis();
  }

  &__copy-icon {
    width: 10px;
    height: 10px;
    cursor: pointer;
  }

  &__status {
    flex-shrink: 0;
    font-size: @font-size-14;
    font-weight: @font-weight-600;
    color: @text-color-primary;
  }

  &__goods {
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
